"""
EXEMPLE D'UTILISATION - ANALYSEUR STATISTIQUE MASSIVE
====================================================

Ce script démontre comment utiliser la nouvelle classe AnalyseurStatistiqueMassive
pour effectuer une étude statistique complète sur toutes les parties du dataset.

Fonctionnalités démontrées :
- Collecte massive des données (toutes parties)
- Calcul des statistiques descriptives (8 métriques + 8 différentiels)
- Analyse des corrélations (120 corrélations)
- Export automatique du rapport complet

Auteur : Basé sur l'expertise d'Analyseur.jl
Date : $(Dates.format(now(), "dd/mm/yyyy"))
"""

# Importer le module principal
include("Analyseur.jl")
using .PredicteurIndex5

# Importer les dépendances nécessaires
using Dates

function exemple_analyse_massive_complete()
    println("🚀 EXEMPLE D'ANALYSE STATISTIQUE MASSIVE")
    println("="^80)
    println("Démonstration de la nouvelle classe AnalyseurStatistiqueMassive")
    println("="^80)
    
    try
        # ÉTAPE 1: Créer l'instance de l'analyseur
        println("\n📊 ÉTAPE 1: Création de l'analyseur...")
        analyseur = AnalyseurStatistiqueMassive{Float64}()
        println("✅ Analyseur créé avec succès")
        println("   • Type: $(typeof(analyseur))")
        println("   • Base: $(analyseur.formulas.base)")
        println("   • Epsilon: $(analyseur.formulas.epsilon)")
        
        # ÉTAPE 2: Spécifier le fichier de données
        println("\n📊 ÉTAPE 2: Configuration du dataset...")
        chemin_fichier = "partie/data.json"  # Ajustez selon votre structure
        
        if !isfile(chemin_fichier)
            println("⚠️ Fichier non trouvé: $chemin_fichier")
            println("💡 Veuillez ajuster le chemin dans le script")
            return
        end
        
        println("✅ Fichier dataset trouvé: $chemin_fichier")
        
        # ÉTAPE 3: Exécuter l'analyse complète
        println("\n📊 ÉTAPE 3: Lancement de l'analyse massive...")
        println("⏱️ Début: $(Dates.format(now(), "HH:MM:SS"))")
        
        # Cette fonction exécute automatiquement toutes les phases :
        # - Collecte massive des données
        # - Calcul des statistiques descriptives
        # - Analyse des corrélations
        # - Export du rapport
        nom_fichier_rapport = analyser_dataset_complet(analyseur, chemin_fichier)
        
        # ÉTAPE 4: Afficher les résultats
        println("\n📊 ÉTAPE 4: Résultats de l'analyse...")
        println("✅ Analyse terminée avec succès !")
        println("📄 Rapport généré: $nom_fichier_rapport")
        
        # Afficher un résumé des résultats
        println("\n📈 RÉSUMÉ DES RÉSULTATS:")
        println("   • Parties analysées: $(analyseur.nb_parties_analysees[])")
        println("   • Mains traitées: $(analyseur.nb_mains_totales[])")
        println("   • Métriques analysées: $(length(analyseur.stats_metriques))/8")
        println("   • Différentiels analysés: $(length(analyseur.stats_differentiels))/8")
        println("   • Corrélations calculées: $(length(analyseur.correlations))")
        println("   • Temps de calcul: $(round(analyseur.temps_calcul_total[], digits=2)) secondes")
        println("   • Mémoire utilisée: $(round(analyseur.memoire_utilisee[], digits=2)) MB")
        
        # ÉTAPE 5: Afficher quelques insights
        println("\n💡 INSIGHTS RAPIDES:")
        
        if !isempty(analyseur.stats_metriques)
            # Métrique la plus stable
            metrique_stable = minimum(analyseur.stats_metriques, by=s -> s.ecart_type)
            println("   🎯 Métrique la plus stable: $(metrique_stable.nom)")
            println("      Écart-type: $(round(metrique_stable.ecart_type, digits=4))")
            
            # Métrique avec la plus grande variance
            metrique_variable = maximum(analyseur.stats_metriques, by=s -> s.variance)
            println("   📊 Métrique la plus variable: $(metrique_variable.nom)")
            println("      Variance: $(round(metrique_variable.variance, digits=4))")
        end
        
        if !isempty(analyseur.correlations)
            # Corrélation la plus forte
            correlations_triees = sort(analyseur.correlations, by=c -> abs(c.correlation_pearson), rev=true)
            corr_max = correlations_triees[1]
            println("   🔗 Corrélation la plus forte:")
            println("      $(corr_max.variable1) ↔ $(corr_max.variable2)")
            println("      Coefficient: $(round(corr_max.correlation_pearson, digits=3))")
            
            # Nombre de corrélations significatives
            nb_significatives = count(c -> c.significatif, analyseur.correlations)
            pourcentage = round(100 * nb_significatives / length(analyseur.correlations), digits=1)
            println("   📈 Corrélations significatives: $nb_significatives/$(length(analyseur.correlations)) ($pourcentage%)")
        end
        
        println("\n🎉 ANALYSE MASSIVE TERMINÉE AVEC SUCCÈS !")
        println("📄 Consultez le rapport détaillé: $nom_fichier_rapport")
        
    catch e
        println("❌ ERREUR LORS DE L'ANALYSE: $e")
        println("🔧 Vérifiez :")
        println("   • Le chemin du fichier de données")
        println("   • La structure du fichier JSON")
        println("   • Les permissions d'écriture")
        rethrow(e)
    end
end

function exemple_analyse_par_etapes()
    println("\n🔬 EXEMPLE D'ANALYSE PAR ÉTAPES")
    println("="^60)
    println("Démonstration du contrôle manuel de chaque phase")
    println("="^60)
    
    try
        # Créer l'analyseur
        analyseur = AnalyseurStatistiqueMassive{Float64}()
        chemin_fichier = "partie/data.json"
        
        if !isfile(chemin_fichier)
            println("⚠️ Fichier non trouvé: $chemin_fichier")
            return
        end
        
        # PHASE 1: Collecte manuelle
        println("\n🔄 PHASE 1: Collecte des données...")
        collecter_donnees_massives(analyseur, chemin_fichier)
        
        # PHASE 2: Statistiques manuelles
        println("\n🔄 PHASE 2: Calcul des statistiques...")
        calculer_statistiques_descriptives(analyseur)
        
        # Afficher quelques statistiques
        if !isempty(analyseur.stats_metriques)
            println("\n📊 APERÇU DES STATISTIQUES MÉTRIQUES:")
            for (i, stats) in enumerate(analyseur.stats_metriques[1:min(3, end)])
                println("   $(i). $(stats.nom):")
                println("      Moyenne: $(round(stats.moyenne, digits=4))")
                println("      Écart-type: $(round(stats.ecart_type, digits=4))")
                println("      Min-Max: [$(round(stats.minimum, digits=4)), $(round(stats.maximum, digits=4))]")
            end
        end
        
        # PHASE 3: Corrélations manuelles
        println("\n🔄 PHASE 3: Calcul des corrélations...")
        calculer_correlations_massives(analyseur)
        
        # PHASE 4: Export manuel
        println("\n🔄 PHASE 4: Export du rapport...")
        nom_fichier = "analyse_manuelle_$(Dates.format(now(), "yyyymmdd_HHMMSS")).txt"
        exporter_analyse_massive(analyseur, nom_fichier)
        
        println("\n✅ ANALYSE PAR ÉTAPES TERMINÉE")
        println("📄 Rapport: $nom_fichier")
        
    catch e
        println("❌ ERREUR: $e")
        rethrow(e)
    end
end

# FONCTION PRINCIPALE
function main()
    println("🎯 EXEMPLES D'UTILISATION - ANALYSEUR STATISTIQUE MASSIVE")
    println("="^80)
    
    println("\nChoisissez un exemple :")
    println("1. Analyse massive complète (automatique)")
    println("2. Analyse par étapes (contrôle manuel)")
    println("3. Les deux exemples")
    
    print("\nVotre choix (1-3): ")
    choix = readline()
    
    if choix == "1"
        exemple_analyse_massive_complete()
    elseif choix == "2"
        exemple_analyse_par_etapes()
    elseif choix == "3"
        exemple_analyse_massive_complete()
        exemple_analyse_par_etapes()
    else
        println("❌ Choix invalide. Exécution de l'exemple complet par défaut...")
        exemple_analyse_massive_complete()
    end
end

# Exécuter si le script est lancé directement
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
