SIGNIFICATION PRÉCISE :
CondT mesure l'entropie conditionnelle moyenne de chaque élément de la séquence, sachant tous les éléments précédents, selon les probabilités théoriques INDEX5.

C'est une mesure de combien d'information nouvelle (en moyenne) chaque élément apporte par rapport à ce qui précède.

EN RÉSUMÉ :
CondT calcule la moyenne des entropies conditionnelles théoriques de chaque position dans la séquence [1:n], en utilisant la règle de chaîne et les probabilités INDEX5.

COMPRÉHENSION EXPERTE DE CondT.jl
Après avoir lu complètement le code ligne par ligne, voici exactement ce que calcule la métrique CondT :

CE QUE FAIT CondT EN LANGAGE NATUREL :
Prend une séquence de valeurs INDEX5 et un nombre n
Extrait les n premières valeurs : sequence[1:n]
Pour chaque position i de 1 à n, calcule l'entropie conditionnelle :
Position i=1 (premier élément) :
Pas de conditionnement possible
Calcule : H_theo(X₁) = -log₂(p_theo(x₁))
Utilise directement la probabilité théorique INDEX5 de la première valeur
Position i>1 (éléments suivants) :
Calcule : H_theo(Xᵢ | X₁,...,Xᵢ₋₁) = H_theo(X₁,...,Xᵢ) - H_theo(X₁,...,Xᵢ₋₁)
Utilise la fonction ShannonT pour calculer les entropies jointes
H_theo(X₁,...,Xᵢ) = entropie théorique de la séquence [1:i]
H_theo(X₁,...,Xᵢ₋₁) = entropie théorique de la séquence [1:i-1]
Additionne toutes les entropies conditionnelles
Divise par n pour obtenir la moyenne
EXEMPLE CONCRET :
Si la séquence [1:3] est ["A", "B", "C"] :

Position 1 :
H_theo(A) = -log₂(p_theo(A))
Position 2 :
H_theo(B|A) = H_theo(A,B) - H_theo(A)
H_theo(A,B) = entropie théorique des valeurs distinctes dans ["A", "B"]
H_theo(A) = entropie théorique des valeurs distinctes dans ["A"]
Position 3 :
H_theo(C|A,B) = H_theo(A,B,C) - H_theo(A,B)
H_theo(A,B,C) = entropie théorique des valeurs distinctes dans ["A", "B", "C"]
H_theo(A,B) = entropie théorique des valeurs distinctes dans ["A", "B"]
Résultat final :
CondT = [H_theo(A) + H_theo(B|A) + H_theo(C|A,B)] / 3