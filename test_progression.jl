"""
TEST DE LA PROGRESSION TEMPS RÉEL
=================================

Script pour tester la nouvelle fonctionnalité de progression
lors du chargement batch des parties.
"""

# Importer les modules
include("CacheJSON.jl")
using .CacheJSON

function test_progression_batch()
    println("🧪 TEST DE LA PROGRESSION BATCH")
    println("="^50)
    
    # Test avec un petit batch pour voir la progression
    println("📊 Test avec 1000 parties...")
    
    try
        parties = CacheJSON.get_parties_batch_avec_progression(
            1:1000, 
            "partie/dataset.json"
        ) do parties_traitees, total_parties, vitesse
            # Callback de progression
            pourcentage = round(100 * parties_traitees / total_parties, digits=1)
            vitesse_arrondie = round(vitesse, digits=0)
            
            # Barre de progression visuelle
            barre_longueur = 20
            parties_remplies = round(Int, barre_longueur * parties_traitees / total_parties)
            barre = "█" ^ parties_remplies * "░" ^ (barre_longueur - parties_remplies)
            
            print("\r📊 [$barre] $parties_traitees/$total_parties ($pourcentage%) - $vitesse_arrondie parties/s")
            flush(stdout)
        end
        
        println("\n✅ Test réussi ! $(length(parties)) parties chargées")
        
    catch e
        println("❌ Erreur lors du test: $e")
    end
end

function demo_progression_visuelle()
    println("\n🎨 DÉMO DE LA PROGRESSION VISUELLE")
    println("="^50)
    
    # Simulation d'une progression
    total = 100
    for i in 1:total
        pourcentage = round(100 * i / total, digits=1)
        vitesse = 50 + rand() * 20  # Vitesse simulée
        
        # Barre de progression
        barre_longueur = 30
        parties_remplies = round(Int, barre_longueur * i / total)
        barre = "█" ^ parties_remplies * "░" ^ (barre_longueur - parties_remplies)
        
        print("\r📊 [$barre] $i/$total ($pourcentage%) - $(round(vitesse, digits=0)) parties/s")
        flush(stdout)
        
        sleep(0.05)  # Simulation du temps de traitement
    end
    
    println("\n✅ Démo terminée !")
end

# Exécuter les tests
if abspath(PROGRAM_FILE) == @__FILE__
    test_progression_batch()
    demo_progression_visuelle()
    
    println("\n🎉 TESTS DE PROGRESSION TERMINÉS")
    println("✅ La progression temps réel est maintenant disponible !")
    println("🚀 Lancez 'julia Analyseur.jl' pour voir la progression en action")
end
